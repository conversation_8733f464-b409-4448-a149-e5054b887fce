# frozen_string_literal: true

require_relative "../../mavis_cli"

module MavisCLI
  module Generate
    class UploadedCohortImports < Dry::CLI::Command
      desc "Generate and upload cohort imports"
      option :patients,
             type: :integer,
             required: true,
             default: 10,
             desc: "Number of patients to create"

      def call(patients:)
        MavisCLI.load_rails

        patient_count = patients.to_i
        puts "Generating cohort import with #{patient_count} patients..."
        progress_bar = MavisCLI.progress_bar(patient_count)

        # Generate the CSV file
        csv_file_path = ::Generate::CohortImports.call(
          patient_count: patient_count,
          progress_bar: progress_bar
        )

        puts "\nCohort import CSV generated: #{csv_file_path}"
        puts "Creating and uploading cohort import..."

        # Read the generated CSV file
        csv_data = File.read(csv_file_path)
        csv_filename = File.basename(csv_file_path)

        # Find or create a user to assign as uploaded_by
        uploaded_by_user = find_or_create_system_user

        # Find the organisation (using the same default as Generate::CohortImports)
        organisation = Organisation.find_by(ods_code: "A9A5A")
        unless organisation
          puts "Error: Organisation with ODS code 'A9A5A' not found"
          return
        end

        # Create the cohort import
        cohort_import = CohortImport.new(
          organisation: organisation,
          uploaded_by: uploaded_by_user,
          csv_data: csv_data,
          csv_filename: csv_filename
        )

        # Load and validate the data
        cohort_import.load_data!
        if cohort_import.invalid?
          puts "Error: Cohort import is invalid:"
          cohort_import.errors.full_messages.each do |error|
            puts "  - #{error}"
          end
          return
        end

        # Save the cohort import
        cohort_import.save!
        puts "Cohort import created with ID: #{cohort_import.id}"

        # Process the import
        puts "Processing cohort import..."
        if cohort_import.slow?
          puts "Import is large (#{cohort_import.rows_count} rows), processing in background..."
          ProcessImportJob.perform_later(cohort_import)
          puts "Import processing started in background"
        else
          ProcessImportJob.perform_now(cohort_import)
          puts "Import processed successfully!"
          puts "  - New records: #{cohort_import.new_record_count}"
          puts "  - Exact duplicates: #{cohort_import.exact_duplicate_record_count}"
        end

        # Clean up the temporary CSV file
        File.delete(csv_file_path) if File.exist?(csv_file_path)
        puts "Temporary CSV file cleaned up"

        puts "\nCohort import completed successfully!"
      end

      private

      def find_or_create_system_user
        # Try to find an existing admin user first
        user = User.where(fallback_role: :admin).first

        return user if user

        # If no admin user exists, try to find any user
        user = User.first
        return user if user

        # If no users exist, we need to create one
        # This should rarely happen in a real system
        organisation = Organisation.find_by(ods_code: "A9A5A")
        user = User.create!(
          email: "<EMAIL>",
          given_name: "System",
          family_name: "User",
          fallback_role: :admin
        )

        # Associate the user with the organisation
        user.organisations << organisation if organisation
        user
      end
    end
  end

  register "generate", aliases: ["g"] do |prefix|
    prefix.register "uploaded-cohort-imports", Generate::UploadedCohortImports
  end
end
